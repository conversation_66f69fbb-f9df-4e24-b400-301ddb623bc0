/*****************************************************************************
* 例程名称		 : 编码器控制舵机
* 说明         : 通过AS5600编码器的旋转控制舵机运动，编码器正转舵机正转，编码器反转舵机反转
* 接线方式     : AS5600编码器连接P00(SCL)和P01(SDA)，舵机PWM信号连接P00
* 注意    		 : 舵机控制脉宽范围500-2500us，中位1500us
*******************************************************************************/

//--包含你要使用的头文件--//
#include "config.h"          //通用配置头文件
#include "AS5600.h"          //AS5600编码器头文件

/*************  本地常量声明    **************/
#define SERVO_MIN_PULSE 500   //舵机最小脉宽(us)
#define SERVO_MAX_PULSE 2500  //舵机最大脉宽(us)
#define SERVO_CENTER_PULSE 1500 //舵机中位脉宽(us)
#define ENCODER_THRESHOLD 10  //编码器变化阈值

/*************  IO口定义    **************/


/*************  本地变量声明    **************/
uint16 encoder_current = 0;   //当前编码器值
uint16 encoder_last = 0;      //上次编码器值
int16 encoder_diff = 0;       //编码器变化量
uint16 servo_pulse = SERVO_CENTER_PULSE; //舵机脉宽

/*************  本地函数声明    **************/
uint16 read_encoder_angle(void);     //读取编码器角度
void servo_control(uint16 pulse);    //舵机控制函数
void encoder_servo_task(void);       //编码器舵机控制任务

/****************  外部函数声明和外部变量声明 *****************/
	
/*******************************************************************************
* 函 数 名         : main
* 函数功能		     : 主函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void main(void)
{
	System_init();  //系统初始化
	PIT_init_ms(0,1);		//用户任务定时器1ms

	PWM_init(PWMB_CH1_P00,50,SERVO_CENTER_PULSE); //初始化PWM，50Hz频率，中位脉宽
	encoder_current = read_encoder_angle(); //读取初始编码器值
	encoder_last = encoder_current;

	Delay_X_mS(1000);
	EUSB = 1;   //IE2相关的中断使能后，需要重新设置EUSB
	EA = 1;  						//允许所有中断

	while(1)
	{
		encoder_servo_task(); //编码器舵机控制任务
		Delay_X_mS(10); //10ms任务周期
	}
}


/*******************************************************************************
* 函 数 名         : read_encoder_angle
* 函数功能		     : 读取AS5600编码器角度值
* 输    入         : 无
* 输    出         : 12位角度值(0-4095)
*******************************************************************************/
uint16 read_encoder_angle(void)
{
	uint16 angle = 0;
	Read_AS5600(); //读取AS5600数据到缓冲区
	angle = (AS5600_BUF[0] << 8) | AS5600_BUF[1]; //合成12位角度值
	angle = angle & 0x0FFF; //取低12位
	return angle;
}

/*******************************************************************************
* 函 数 名         : servo_control
* 函数功能		     : 控制舵机PWM脉宽
* 输    入         : pulse - 脉宽值(us)
* 输    出         : 无
*******************************************************************************/
void servo_control(uint16 pulse)
{
	pulse = Limit_int(SERVO_MIN_PULSE, pulse, SERVO_MAX_PULSE); //限制脉宽范围
	PWM_change(PWMB_CH1_P00, pulse); //更改PWM脉宽
}

/*******************************************************************************
* 函 数 名         : encoder_servo_task
* 函数功能		     : 编码器舵机控制任务
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void encoder_servo_task(void)
{
	encoder_current = read_encoder_angle(); //读取当前编码器值

	//计算编码器变化量，处理跨零点情况
	encoder_diff = encoder_current - encoder_last;
	if(encoder_diff > 2048) encoder_diff -= 4096; //跨零点向下
	if(encoder_diff < -2048) encoder_diff += 4096; //跨零点向上

	//根据编码器变化控制舵机
	if(encoder_diff > ENCODER_THRESHOLD) //编码器正转
	{
		servo_pulse += (encoder_diff >> 2); //正转，增加脉宽
	}
	else if(encoder_diff < -ENCODER_THRESHOLD) //编码器反转
	{
		servo_pulse += (encoder_diff >> 2); //反转，减少脉宽
	}

	servo_control(servo_pulse); //控制舵机
	encoder_last = encoder_current; //更新上次值
}

void TM0_isr(void)  //1ms用户任务定时器中断
{
	if(++T0_cnt==60000)	T0_cnt=0; //形成循环变量
}