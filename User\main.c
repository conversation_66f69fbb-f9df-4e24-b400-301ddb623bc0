/*****************************************************************************
* 例程名称		 : 空工程
* 说明         : 新建项目可以直接复制空工程开始编程。
* 接线方式     : ---
* 注意    		 : ---
*******************************************************************************/

//--包含你要使用的头文件--//
#include "config.h"          //通用配置头文件

/*************  本地常量声明    **************/


/*************  IO口定义    **************/


/*************  本地变量声明    **************/

	

/*************  本地函数声明    **************/

/****************  外部函数声明和外部变量声明 *****************/
	
/*******************************************************************************
* 函 数 名         : main
* 函数功能		     : 主函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/	
void main(void)
{
	System_init();  //系统初始化	
	PIT_init_ms(0,1);		//用户任务定时器1ms

	PWM_init(PWMB_CH1_P00,50,750);
	Delay_X_mS(1000);
	EUSB = 1;   //IE2相关的中断使能后，需要重新设置EUSB
	EA = 1;  						//允许所有中断
	
	while(1)
	{	
		PWM_change(PWMB_CH1_P00,250);
		Delay_X_mS(1000);
		PWM_change(PWMB_CH1_P00,1250);
		Delay_X_mS(1000);
	}
}


void TM0_isr(void)  //1ms用户任务定时器中断
{
	if(++T0_cnt==60000)	T0_cnt=0; //形成循环变量
}