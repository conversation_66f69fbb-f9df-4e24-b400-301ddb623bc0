# 磁编码器控制舵机系统

## 功能概述
基于磁编码器旋转控制PWM来精确控制舵机运动的简洁系统，通过编码器的正转和反转实现舵机位置的精确调节。

## 主要功能

### 1. 磁编码器控制
- **旋转检测**: 通过INT1中断实时检测磁编码器旋转
- **方向识别**: 自动识别编码器正转和反转方向
- **PWM调节**: 根据旋转方向增加或减少PWM值
- **实时响应**: 编码器旋转立即更新舵机位置

### 2. 舵机精确控制
- **PWM范围**: 250us-1250us的完整舵机控制范围
- **步长调节**: 每次旋转调节10us的PWM步长
- **边界保护**: 自动限制PWM值在有效范围内
- **平滑控制**: 连续旋转实现舵机位置的平滑调节

### 3. 系统特点
- **简洁设计**: 仅82行代码实现完整功能
- **高效响应**: 中断驱动的实时控制
- **精确定位**: 10us步长提供精确的位置控制
- **稳定可靠**: 边界检查确保系统安全运行
- **模式切换**: 通过按键在自动/手动模式间切换

### 4. 工作模式
- **自动模式**: 红外管检测到物体自动执行夹取流程
- **手动模式**: 停止自动控制，等待手动指令

## 硬件连接

| 功能 | 引脚 | 说明 |
|------|------|------|
| 舵机PWM | P0^0 | PWM输出控制舵机位置 |
| 磁编码器 | P3^4 | 编码器旋转信号输入(INT1中断) |

## 工作流程

### 磁编码器控制流程：
1. **系统初始化**: 舵机初始化到中位位置(750us)
2. **编码器检测**: INT1中断实时检测编码器旋转
3. **PWM调节**:
   - 编码器正转: PWM值增加10us
   - 编码器反转: PWM值减少10us
   - 自动限制在250us-1250us范围内
4. **舵机响应**: PWM变化立即更新舵机位置
5. **连续控制**: 持续旋转实现精确位置调节

### 控制参数：
- **PWM范围**: 250us(最小) - 1250us(最大)
- **中位位置**: 750us
- **调节步长**: 10us/步
- **中断触发**: 下降沿触发INT1中断
- **方向检测**: P3^4引脚电平判断旋转方向

## 参数配置

### 舵机控制参数
```c
#define SERVO_CLOSE_POS 1250  // 机械臂收缩位置
#define SERVO_OPEN_POS  250   // 机械臂松开位置
#define SERVO_NEUTRAL   750   // 舵机中位
```

### 控制逻辑
- **收缩条件**: IR_ERR > 0 (红外检测到目标)
- **松开条件**: IR_ERR < 0 (红外偏离目标)
- **中位条件**: IR_ERR = 0 (无偏差)
- **运动步长**: 5个单位/次 (平滑运动)
- **PWM频率**: 50Hz
- **更新周期**: 主循环实时更新

### 编码器控制原理
- **中断触发**: 编码器旋转触发INT1中断
- **方向检测**: 读取P3^4引脚电平判断旋转方向
- **PWM更新**: 根据方向增加或减少PWM值
- **控制逻辑**:
  - 正转(EC11_DIR=1): servo_pwm += PWM_STEP
  - 反转(EC11_DIR=0): servo_pwm -= PWM_STEP
  - 边界检查: 限制在SERVO_MIN_POS到SERVO_MAX_POS范围

## 核心算法

```c
void encoder_control(void) {
    if(EC11_R_flag) {  // 编码器旋转
        if(EC11_DIR) {  // 正转，增加PWM
            servo_pwm += PWM_STEP;
            if(servo_pwm > SERVO_MAX_POS) servo_pwm = SERVO_MAX_POS;
        } else {  // 反转，减少PWM
            servo_pwm -= PWM_STEP;
            if(servo_pwm < SERVO_MIN_POS) servo_pwm = SERVO_MIN_POS;
        }
        PWM_change(PWMB_CH1_P00, servo_pwm);  // 更新PWM
        EC11_R_flag=0;
    }
}
```

## 使用说明

1. **系统启动**: 上电后舵机自动初始化到中位位置(750us)
2. **编码器控制**:
   - 顺时针旋转编码器 → 舵机PWM值增加 → 舵机正转
   - 逆时针旋转编码器 → 舵机PWM值减少 → 舵机反转
   - 连续旋转可实现精确的位置调节
3. **位置范围**: PWM值自动限制在250us-1250us范围内
4. **实时响应**: 编码器每次旋转立即更新舵机位置
## 注意事项

- 确保磁编码器与P3^4引脚正确连接
- 舵机供电电压应稳定在4.8V-6V之间
- 编码器信号线应避免干扰，确保信号稳定
- 舵机运动范围内应无障碍物
- 系统响应速度取决于编码器旋转速度

## 技术特点

### 代码简洁性
- **总代码量**: 仅82行代码实现完整功能
- **核心算法**: 编码器控制函数仅17行代码
- **中断处理**: 简洁高效的中断服务函数
- **无冗余**: 移除所有不必要的功能模块

### 实时性能
- **中断响应**: 编码器旋转立即触发中断
- **即时更新**: PWM值变化立即反映到舵机
- **无延迟**: 主循环连续检测编码器状态
- **高精度**: 10us步长提供精确控制

### 系统稳定性
- **边界保护**: 自动限制PWM值在有效范围
- **防抖动**: 中断标志位确保单次处理
- **可靠性**: 简单的逻辑减少故障点
- **易维护**: 清晰的代码结构便于调试
